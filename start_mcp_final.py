import threading
import time
import asyncio
import sys
import os
from io import StringIO
import socket
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import gradio_infinitetalk as gradio_ui
import main as comfy_server

# --- 1. [新增] 日志捕获的核心逻辑 ---
class OutputLogger(StringIO):
    """
    一个自定义的"文件"，它捕获所有写入其中的内容 (print语句)
    并将其存储在一个内部列表中。
    """
    def __init__(self):
        super().__init__()
        self._logs = []
        self._lock = threading.Lock()

    def write(self, msg):
        # 将捕获的每条消息（通常是一行）添加到列表中
        with self._lock:
            self._logs.append(msg)
    
    def get_logs(self):
        """返回所有捕获到的日志，并清空缓冲区"""
        with self._lock:
            # 将列表中的所有日志行连接成一个字符串
            full_log = "".join(self._logs)
            self._logs.clear() # 清空以避免重复显示
            return full_log

# 创建一个全局的日志捕获器实例
GLOBAL_LOG_CATCHER = OutputLogger()

def find_free_port():
    """找到一个可用的端口"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def start_comfy_server():
    print("\n--- ComfyUI 服务器线程已启动 ---")
    try:
        event_loop, _, start_all_func = comfy_server.start_comfyui()
        asyncio.set_event_loop(event_loop)
        event_loop.run_until_complete(start_all_func())
        print("--- ComfyUI 服务器线程已结束 ---")
    except Exception as e:
        print(f"!!! ComfyUI 服务器线程遇到错误: {e}")
        import traceback
        traceback.print_exc()

def patch_gradio_networking():
    """修补Gradio的网络检查以避免连接问题"""
    try:
        import gradio.networking as networking
        
        # 保存原始函数
        original_url_ok = networking.url_ok
        
        def patched_url_ok(url):
            """总是返回True，跳过网络检查"""
            print(f"    - 跳过网络检查: {url}")
            return True
        
        # 替换函数
        networking.url_ok = patched_url_ok
        print("    - 已修补Gradio网络检查函数")
        return True
    except Exception as e:
        print(f"    - 修补Gradio网络检查失败: {e}")
        return False

def launch_gradio_with_mcp():
    """
    启动带有MCP支持的Gradio，使用修补的网络检查
    """
    print("[4/4] 正在启动 Gradio Web UI...")
    
    # 修补网络检查
    patch_gradio_networking()
    
    # 找到可用端口
    port = find_free_port()
    print(f"    - 使用端口: {port}")
    
    try:
        print("    - 尝试启动MCP服务器模式...")
        
        # 尝试启动MCP模式
        gradio_ui.demo.launch(
            server_name="0.0.0.0",
            server_port=port,
            inbrowser=False,
            mcp_server=True,
            share=True,
            show_error=False,  # 禁用错误显示避免网络检查
            quiet=True,        # 安静模式
            prevent_thread_lock=False
        )
        print("    - MCP服务器模式启动成功!")
        
    except Exception as e:
        print(f"    - MCP服务器模式启动失败: {e}")
        print("    - 回退到普通模式...")
        
        try:
            # 回退到普通模式
            gradio_ui.demo.launch(
                server_name="0.0.0.0",
                server_port=port + 1,
                inbrowser=False,
                mcp_server=False,
                share=True,
                show_error=False,
                quiet=True,
                prevent_thread_lock=False
            )
            print("    - 普通模式启动成功!")
            
        except Exception as e2:
            print(f"    - 普通模式也启动失败: {e2}")
            print("    - 尝试最简单的启动方式...")
            
            try:
                # 最简单的启动方式
                gradio_ui.demo.launch(
                    share=True,
                    inbrowser=False,
                    show_error=False,
                    quiet=True
                )
                print("    - 简单模式启动成功!")
                
            except Exception as e3:
                print(f"    - 所有启动方式都失败了: {e3}")
                print("    - 请检查网络设置或防火墙配置")
                raise e3

if __name__ == "__main__":
    
    print("[3/4] 正在后台启动 ComfyUI 服务器...")
    server_thread = threading.Thread(target=start_comfy_server, daemon=True)
    server_thread.start()
    initialization_delay = 7 # 如果您的电脑或模型加载较慢，可以适当增加这个时间
    print(f"    - 等待 {initialization_delay} 秒让服务器完成初始化...")
    time.sleep(initialization_delay)
    print("    - ComfyUI 服务器应该已经在后台运行。")
    
    launch_gradio_with_mcp()
    print("--- Gradio UI 已关闭，程序即将退出 ---")
