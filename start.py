import threading
import time
import asyncio
import sys
import os
from io import StringIO
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import gradio_infinitetalk as gradio_ui
import main as comfy_server

# --- 1. [新增] 日志捕获的核心逻辑 ---
class OutputLogger(StringIO):
    """
    一个自定义的“文件”，它捕获所有写入其中的内容 (print语句)
    并将其存储在一个内部列表中。
    """
    def __init__(self):
        super().__init__()
        self._logs = []
        self._lock = threading.Lock()

    def write(self, msg):
        # 将捕获的每条消息（通常是一行）添加到列表中
        with self._lock:
            self._logs.append(msg)
    
    def get_logs(self):
        """返回所有捕获到的日志，并清空缓冲区"""
        with self._lock:
            # 将列表中的所有日志行连接成一个字符串
            full_log = "".join(self._logs)
            self._logs.clear() # 清空以避免重复显示
            return full_log

# 创建一个全局的日志捕获器实例
GLOBAL_LOG_CATCHER = OutputLogger()

def start_comfy_server():
    print("\n--- ComfyUI 服务器线程已启动 ---")
    try:
        event_loop, _, start_all_func = comfy_server.start_comfyui()
        asyncio.set_event_loop(event_loop)
        event_loop.run_until_complete(start_all_func())
        print("--- ComfyUI 服务器线程已结束 ---")
    except Exception as e:
        print(f"!!! ComfyUI 服务器线程遇到错误: {e}")
        import traceback
        traceback.print_exc()
        
if __name__ == "__main__":
    
    print("[3/4] 正在后台启动 ComfyUI 服务器...")
    server_thread = threading.Thread(target=start_comfy_server, daemon=True)
    server_thread.start()
    initialization_delay = 7 # 如果您的电脑或模型加载较慢，可以适当增加这个时间
    print(f"    - 等待 {initialization_delay} 秒让服务器完成初始化...")
    time.sleep(initialization_delay)
    print("    - ComfyUI 服务器应该已经在后台运行。")
    print("[4/4] 正在启动 Gradio Web UI...")
    gradio_ui.demo.launch(server_name="0.0.0.0", server_port=7862, inbrowser=False, mcp_server=True)
    print("--- Gradio UI 已关闭，程序即将退出 ---")